{"risk_score": 0.8333333333333334, "prediction": 1, "label": "预警", "confidence": 0.8333333333333334, "successful_algorithms": ["expert"], "failed_algorithms": ["precursor", "anomaly"], "fusion_weights": {"precursor": 0.5, "anomaly": 0.3, "expert": 0.2}, "filename": "../泸201H1-4实时数据.csv", "processing_time": 12.080858, "timestamp": "2025-07-27T17:43:32.544061", "algorithm_results": {"precursor": {"algorithm": "precursor", "success": false, "risk_score": 0.0, "prediction": 0, "confidence": 0.0, "error": "前驱信号检测执行失败: Traceback (most recent call last):\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\run.py\", line 4, in <module>\n    from exp.exp_long_term_forecasting import Exp_Long_Term_Forecast\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\exp\\exp_long_term_forecasting.py\", line 2, in <module>\n    from exp.exp_basic import Exp_Basic\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\exp\\exp_basic.py\", line 3, in <module>\n    from models import Autoformer, Transformer, TimesNet, Nonstationary_Transformer, DLinear, FEDformer, \\\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\models\\__init__.py\", line 2, in <module>\n    from models.Transformer import Model as Transformer\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\models\\Transformer.py\", line 5, in <module>\n    from layers.SelfAttention_Family import FullAttention, AttentionLayer\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\前驱信号检测\\layers\\SelfAttention_Family.py\", line 6, in <module>\n    from reformer_pytorch import LSHSelfAttention\nModuleNotFoundError: No module named 'reformer_pytorch'\n", "details": {}}, "anomaly": {"algorithm": "anomaly", "success": false, "risk_score": 0.0, "prediction": 0, "confidence": 0.0, "error": "异常检测执行失败: Traceback (most recent call last):\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\异常检测\\run.py\", line 4, in <module>\n    from exp.exp_long_term_forecasting import Exp_Long_Term_Forecast\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\异常检测\\exp\\exp_long_term_forecasting.py\", line 2, in <module>\n    from exp.exp_basic import Exp_Basic\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\异常检测\\exp\\exp_basic.py\", line 3, in <module>\n    from models import Autoformer, Transformer, TimesNet, Nonstationary_Transformer, DLinear, FEDformer, \\\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\异常检测\\models\\Transformer.py\", line 5, in <module>\n    from layers.SelfAttention_Family import FullAttention, AttentionLayer\n  File \"D:\\PyCharm\\kazuan\\mix_algo\\异常检测\\layers\\SelfAttention_Family.py\", line 6, in <module>\n    from reformer_pytorch import LSHSelfAttention\nModuleNotFoundError: No module named 'reformer_pytorch'\n", "details": {}}, "expert": {"algorithm": "expert", "success": true, "risk_score": 0.8333333333333334, "prediction": 1, "confidence": 0.8333333333333334, "details": {"max_score": 5.0, "avg_score": 5.0, "score_threshold": 4, "window_count": 41228}}}}